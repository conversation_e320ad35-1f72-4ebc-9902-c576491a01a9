"""
NEUROGLYPH Formal Logic Engine
API deduce(premises, goal) -> ProofTree
"""

import logging
from typing import List, Dict, Set, Optional, Tuple
from dataclasses import dataclass

from .formula import Formula, Variable, Term, Predicate, Implication, Conjunction, Universal
from .proof_tree import ProofTree, ProofStep, ProofRule
from .unify import SymbolicUnifier, Substitution

logger = logging.getLogger(__name__)


@dataclass
class DeductionResult:
    """Risultato di deduzione logica."""
    
    success: bool
    proof_tree: Optional[ProofTree] = None
    error_message: str = ""
    steps_count: int = 0
    depth: int = 0
    duration: float = 0.0


class FormalLogicEngine:
    """
    Motore di deduzione logica formale.
    
    Features:
    - Modus ponens, universal instantiation
    - Backward chaining goal-driven
    - Proof tree generation
    - Symbolic unification
    """
    
    def __init__(self, max_depth: int = 10, max_steps: int = 100):
        """
        Inizializza logic engine.
        
        Args:
            max_depth: Profondità massima reasoning
            max_steps: Numero massimo step per proof
        """
        self.max_depth = max_depth
        self.max_steps = max_steps
        self.unifier = SymbolicUnifier()
        
        # Statistiche
        self.proofs_attempted = 0
        self.proofs_successful = 0
        
        logger.info(f"🧠 FormalLogicEngine inizializzato (max_depth={max_depth}, max_steps={max_steps})")
    
    def deduce(self, premises: List[Formula], goal: Formula) -> DeductionResult:
        """
        Deduce goal da premises usando backward chaining.
        
        Args:
            premises: Lista premesse
            goal: Formula da dimostrare
            
        Returns:
            DeductionResult con proof tree se successo
        """
        import time
        
        start_time = time.time()
        self.proofs_attempted += 1
        
        logger.debug(f"🎯 Tentativo deduzione: {goal}")
        logger.debug(f"   Premesse: {[str(p) for p in premises]}")
        
        try:
            # Crea proof tree
            proof_tree = ProofTree(conclusion=goal, premises=premises)
            
            # Aggiungi premesse al proof tree
            premise_steps = []
            for premise in premises:
                step_id = proof_tree.add_premise(premise)
                premise_steps.append(proof_tree.steps[-1])
            
            # Backward chaining
            success = self._backward_chain(proof_tree, goal, premise_steps, depth=0)
            
            if success:
                proof_tree.validate()
                self.proofs_successful += 1
                
                duration = time.time() - start_time
                
                logger.info(f"✅ Deduzione riuscita in {duration:.3f}s ({len(proof_tree.steps)} steps)")
                
                return DeductionResult(
                    success=True,
                    proof_tree=proof_tree,
                    steps_count=len(proof_tree.steps),
                    depth=proof_tree.depth,
                    duration=duration
                )
            else:
                duration = time.time() - start_time
                
                logger.warning(f"❌ Deduzione fallita in {duration:.3f}s")
                
                return DeductionResult(
                    success=False,
                    error_message="Goal not derivable from premises",
                    duration=duration
                )
                
        except Exception as e:
            duration = time.time() - start_time
            
            logger.error(f"❌ Errore deduzione: {e}")
            
            return DeductionResult(
                success=False,
                error_message=str(e),
                duration=duration
            )
    
    def _backward_chain(self, proof_tree: ProofTree, goal: Formula, 
                       available_steps: List[ProofStep], depth: int) -> bool:
        """
        Backward chaining ricorsivo.
        
        Args:
            proof_tree: Proof tree in costruzione
            goal: Goal da dimostrare
            available_steps: Step disponibili
            depth: Profondità corrente
            
        Returns:
            True se goal derivabile
        """
        if depth > self.max_depth:
            logger.debug(f"⚠️ Max depth raggiunta: {depth}")
            return False
        
        if len(proof_tree.steps) > self.max_steps:
            logger.debug(f"⚠️ Max steps raggiunto: {len(proof_tree.steps)}")
            return False
        
        logger.debug(f"🔍 Backward chain (depth={depth}): {goal}")
        
        # 1. Goal già disponibile?
        for step in available_steps:
            if step.formula == goal:
                logger.debug(f"✅ Goal trovato direttamente: {step}")
                return True
        
        # 2. Unificazione con step disponibili
        for step in available_steps:
            substitution = self.unifier.unify_formulas(step.formula, goal)
            if substitution is not None:
                logger.debug(f"✅ Goal unificabile: {step.formula} ≈ {goal}")
                return True
        
        # 3. Modus ponens: cerca P → Goal, poi dimostra P
        for step in available_steps:
            if isinstance(step.formula, Implication):
                if step.formula.consequent == goal:
                    # Trovata implicazione P → Goal, dimostra P
                    antecedent = step.formula.antecedent
                    logger.debug(f"🔗 Modus ponens: dimostra {antecedent} per ottenere {goal}")
                    
                    if self._backward_chain(proof_tree, antecedent, available_steps, depth + 1):
                        # Trova step per antecedent
                        antecedent_step = None
                        for s in proof_tree.steps:
                            if s.formula == antecedent:
                                antecedent_step = s
                                break
                        
                        if antecedent_step:
                            # Applica modus ponens
                            proof_tree.apply_modus_ponens(step, antecedent_step)
                            return True
        
        # 4. Universal instantiation: cerca ∀x P(x), unifica P(x) con goal
        for step in available_steps:
            if isinstance(step.formula, Universal):
                universal_formula = step.formula
                
                # Prova unificazione con formula interna
                substitution = self.unifier.unify_formulas(universal_formula.formula, goal)
                if substitution is not None:
                    # Trova termine per istanziazione
                    var = universal_formula.variable
                    if var in substitution:
                        term = substitution[var]
                        logger.debug(f"🔗 Universal instantiation: {universal_formula} con {term}")
                        
                        proof_tree.apply_universal_instantiation(step, term)
                        return True
        
        # 5. Conjunction elimination: cerca P ∧ Q, goal = P o Q
        for step in available_steps:
            if isinstance(step.formula, Conjunction):
                conjunction = step.formula
                
                if conjunction.left == goal:
                    logger.debug(f"🔗 Conjunction elimination (left): {conjunction} ⊢ {goal}")
                    proof_tree.apply_conjunction_elimination(step, left=True)
                    return True
                
                if conjunction.right == goal:
                    logger.debug(f"🔗 Conjunction elimination (right): {conjunction} ⊢ {goal}")
                    proof_tree.apply_conjunction_elimination(step, left=False)
                    return True
        
        # 6. Conjunction introduction: goal = P ∧ Q, dimostra P e Q
        if isinstance(goal, Conjunction):
            left_goal = goal.left
            right_goal = goal.right
            
            logger.debug(f"🔗 Conjunction introduction: dimostra {left_goal} e {right_goal}")
            
            # Dimostra entrambi i lati
            left_success = self._backward_chain(proof_tree, left_goal, available_steps, depth + 1)
            right_success = self._backward_chain(proof_tree, right_goal, available_steps, depth + 1)
            
            if left_success and right_success:
                # Trova step per entrambi i lati
                left_step = None
                right_step = None
                
                for s in proof_tree.steps:
                    if s.formula == left_goal:
                        left_step = s
                    if s.formula == right_goal:
                        right_step = s
                
                if left_step and right_step:
                    proof_tree.apply_conjunction_introduction(left_step, right_step)
                    return True
        
        logger.debug(f"❌ Goal non derivabile: {goal}")
        return False
    
    def get_statistics(self) -> Dict[str, any]:
        """Ottiene statistiche engine."""
        success_rate = self.proofs_successful / self.proofs_attempted if self.proofs_attempted > 0 else 0.0
        
        return {
            'proofs_attempted': self.proofs_attempted,
            'proofs_successful': self.proofs_successful,
            'success_rate': success_rate,
            'max_depth': self.max_depth,
            'max_steps': self.max_steps
        }
    
    def reset_statistics(self):
        """Reset statistiche."""
        self.proofs_attempted = 0
        self.proofs_successful = 0


# Factory function per uso rapido
def prove(premises: List[Formula], goal: Formula, 
          max_depth: int = 10, max_steps: int = 100) -> DeductionResult:
    """
    Prova goal da premises.
    
    Args:
        premises: Lista premesse
        goal: Formula da dimostrare
        max_depth: Profondità massima
        max_steps: Step massimi
        
    Returns:
        DeductionResult
    """
    engine = FormalLogicEngine(max_depth=max_depth, max_steps=max_steps)
    return engine.deduce(premises, goal)
