{"version": "4.0", "timestamp": 1749164008.801989, "total_symbols": 37, "categories": {"logic": {"count": 11, "symbols": [{"symbol": "⊢", "name": "entails", "category": "logic", "unicode_codepoint": 8866, "description": "Logical entailment"}, {"symbol": "⊨", "name": "models", "category": "logic", "unicode_codepoint": 8872, "description": "Semantic entailment"}, {"symbol": "⊥", "name": "bottom", "category": "logic", "unicode_codepoint": 8869, "description": "Contradiction/false"}, {"symbol": "⊤", "name": "top", "category": "logic", "unicode_codepoint": 8868, "description": "Tautology/true"}, {"symbol": "∀", "name": "forall", "category": "logic", "unicode_codepoint": 8704, "description": "Universal quantifier"}, {"symbol": "∃", "name": "exists", "category": "logic", "unicode_codepoint": 8707, "description": "Existential quantifier"}, {"symbol": "¬", "name": "not", "category": "logic", "unicode_codepoint": 172, "description": "Logical negation"}, {"symbol": "∧", "name": "and", "category": "logic", "unicode_codepoint": 8743, "description": "Logical conjunction"}, {"symbol": "∨", "name": "or", "category": "logic", "unicode_codepoint": 8744, "description": "Logical disjunction"}, {"symbol": "→", "name": "implies", "category": "logic", "unicode_codepoint": 8594, "description": "Logical implication"}, {"symbol": "↔", "name": "iff", "category": "logic", "unicode_codepoint": 8596, "description": "Logical biconditional"}]}, "math": {"count": 11, "symbols": [{"symbol": "∑", "name": "sum", "category": "math", "unicode_codepoint": 8721, "description": "Summation"}, {"symbol": "∏", "name": "product", "category": "math", "unicode_codepoint": 8719, "description": "Product"}, {"symbol": "∫", "name": "integral", "category": "math", "unicode_codepoint": 8747, "description": "Integral"}, {"symbol": "∂", "name": "partial", "category": "math", "unicode_codepoint": 8706, "description": "Partial derivative"}, {"symbol": "∇", "name": "nabla", "category": "math", "unicode_codepoint": 8711, "description": "Gradient operator"}, {"symbol": "∞", "name": "infinity", "category": "math", "unicode_codepoint": 8734, "description": "Infinity"}, {"symbol": "≈", "name": "approx", "category": "math", "unicode_codepoint": 8776, "description": "Approximately equal"}, {"symbol": "≡", "name": "equiv", "category": "math", "unicode_codepoint": 8801, "description": "Equivalent"}, {"symbol": "≠", "name": "neq", "category": "math", "unicode_codepoint": 8800, "description": "Not equal"}, {"symbol": "≤", "name": "leq", "category": "math", "unicode_codepoint": 8804, "description": "Less than or equal"}, {"symbol": "≥", "name": "geq", "category": "math", "unicode_codepoint": 8805, "description": "Greater than or equal"}]}, "code": {"count": 9, "symbols": [{"symbol": "λ", "name": "lambda", "category": "code", "unicode_codepoint": 955, "description": "Lambda function"}, {"symbol": "↦", "name": "mapsto", "category": "code", "unicode_codepoint": 8614, "description": "Maps to"}, {"symbol": "⟨", "name": "langle", "category": "code", "unicode_codepoint": 10216, "description": "Left angle bracket"}, {"symbol": "⟩", "name": "rangle", "category": "code", "unicode_codepoint": 10217, "description": "Right angle bracket"}, {"symbol": "⟦", "name": "llbracket", "category": "code", "unicode_codepoint": 10214, "description": "Left double bracket"}, {"symbol": "⟧", "name": "rrbracket", "category": "code", "unicode_codepoint": 10215, "description": "Right double bracket"}, {"symbol": "⊕", "name": "oplus", "category": "code", "unicode_codepoint": 8853, "description": "Exclusive or"}, {"symbol": "⊗", "name": "otimes", "category": "code", "unicode_codepoint": 8855, "description": "Tensor product"}, {"symbol": "⊙", "name": "odot", "category": "code", "unicode_codepoint": 8857, "description": "Dot product"}]}, "meta": {"count": 6, "symbols": [{"symbol": "⟪", "name": "meta_open", "category": "meta", "unicode_codepoint": 10218, "description": "Meta-level opening"}, {"symbol": "⟫", "name": "meta_close", "category": "meta", "unicode_codepoint": 10219, "description": "Meta-level closing"}, {"symbol": "⟬", "name": "meta_left", "category": "meta", "unicode_codepoint": 10220, "description": "Meta-level left"}, {"symbol": "⟭", "name": "meta_right", "category": "meta", "unicode_codepoint": 10221, "description": "Meta-level right"}, {"symbol": "⟮", "name": "meta_paren_left", "category": "meta", "unicode_codepoint": 10222, "description": "Meta-level parenthesis left"}, {"symbol": "⟯", "name": "meta_paren_right", "category": "meta", "unicode_codepoint": 10223, "description": "Meta-level parenthesis right"}]}}, "symbol_hash": "8e738ec0f33cef3301ef9706711f5a406f0f605a87e3721fd55c0c1fbe1af921", "symbols": ["¬", "λ", "→", "↔", "↦", "∀", "∂", "∃", "∇", "∏", "∑", "∞", "∧", "∨", "∫", "≈", "≠", "≡", "≤", "≥", "⊕", "⊗", "⊙", "⊢", "⊤", "⊥", "⊨", "⟦", "⟧", "⟨", "⟩", "⟪", "⟫", "⟬", "⟭", "⟮", "⟯"]}