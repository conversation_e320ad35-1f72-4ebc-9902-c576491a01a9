#!/usr/bin/env python3
"""
NEUROGLYPH CLI
Comando ng learner status per monitoring learning components
"""

import sys
import os
import argparse
import json
from typing import Dict, Any

# Aggiungi path per import NEUROGLYPH
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from neuroglyph.cognitive.adaptive_patcher import NGAdaptivePatcher
    from neuroglyph.learning.pattern_extractor import PatternExtractor
    from neuroglyph.learning.knowledge_graph_builder import KnowledgeGraphBuilder
    NEUROGLYPH_AVAILABLE = True
except ImportError as e:
    print(f"❌ Errore import NEUROGLYPH: {e}")
    NEUROGLYPH_AVAILABLE = False


class Colors:
    """Colori ANSI per output."""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'


def format_number(num: float, precision: int = 2) -> str:
    """Formatta numero con colori."""
    if num >= 0.8:
        return f"{Colors.OKGREEN}{num:.{precision}f}{Colors.ENDC}"
    elif num >= 0.6:
        return f"{Colors.WARNING}{num:.{precision}f}{Colors.ENDC}"
    else:
        return f"{Colors.FAIL}{num:.{precision}f}{Colors.ENDC}"


def format_count(count: int) -> str:
    """Formatta contatore con colori."""
    if count >= 100:
        return f"{Colors.OKGREEN}{count:,}{Colors.ENDC}"
    elif count >= 10:
        return f"{Colors.WARNING}{count:,}{Colors.ENDC}"
    else:
        return f"{Colors.FAIL}{count:,}{Colors.ENDC}"


def print_header(title: str):
    """Stampa header con stile."""
    print(f"\n{Colors.BOLD}{Colors.HEADER}{'='*60}{Colors.ENDC}")
    print(f"{Colors.BOLD}{Colors.HEADER}{title:^60}{Colors.ENDC}")
    print(f"{Colors.BOLD}{Colors.HEADER}{'='*60}{Colors.ENDC}\n")


def print_section(title: str):
    """Stampa sezione con stile."""
    print(f"{Colors.BOLD}{Colors.OKBLUE}{title}{Colors.ENDC}")
    print(f"{Colors.OKBLUE}{'-'*len(title)}{Colors.ENDC}")


def learner_status(db_path: str = "learning.db"):
    """
    Comando ng learner status.
    
    Stampa:
    - Pattern totali
    - Accuracy 24h
    - Top-5 pattern nuovi
    """
    if not NEUROGLYPH_AVAILABLE:
        print(f"{Colors.FAIL}❌ NEUROGLYPH non disponibile{Colors.ENDC}")
        return 1
    
    try:
        print_header("NEUROGLYPH LEARNER STATUS")
        
        # Inizializza componenti
        print(f"{Colors.OKCYAN}🔄 Inizializzazione componenti...{Colors.ENDC}")
        
        patcher = NGAdaptivePatcher(db_path=db_path)
        pattern_extractor = PatternExtractor(db_path=db_path)
        kg_builder = KnowledgeGraphBuilder(db_path.replace('.db', '_kg.db'))
        
        # Statistiche generali
        print_section("📊 Statistiche Generali")
        
        learning_stats = patcher.get_learning_statistics()
        
        print(f"Learning disponibile: {Colors.OKGREEN if learning_stats.get('learning_available') else Colors.FAIL}{'✅' if learning_stats.get('learning_available') else '❌'}{Colors.ENDC}")
        print(f"Patch totali: {format_count(learning_stats.get('total_patches', 0))}")
        print(f"Success rate: {format_number(learning_stats.get('success_rate', 0.0))}")
        print(f"Patch 24h: {format_count(learning_stats.get('patches_24h', 0))}")
        
        # Pattern Extractor
        print_section("🔍 Pattern Extractor")
        
        pe_stats = pattern_extractor.get_stats()
        
        print(f"Pattern totali: {format_count(pe_stats.get('total_patterns', 0))}")
        print(f"Estrazioni: {format_count(pe_stats.get('extraction_count', 0))}")
        print(f"Confidence media: {format_number(pe_stats.get('avg_confidence', 0.0))}")
        print(f"Support medio: {format_number(pe_stats.get('avg_support', 0.0), 0)}")
        
        # Circuit breaker status
        cb = pe_stats.get('circuit_breaker', {})
        cb_active = cb.get('consecutive_failures', 0) >= cb.get('failure_threshold', 10)
        print(f"Circuit breaker: {Colors.FAIL if cb_active else Colors.OKGREEN}{'🚨 ATTIVO' if cb_active else '✅ OK'}{Colors.ENDC}")
        
        # Knowledge Graph
        print_section("🧠 Knowledge Graph")
        
        kg_stats = kg_builder.get_summary()
        
        print(f"Nodi totali: {format_count(kg_stats.get('total_nodes', 0))}")
        print(f"Edge totali: {format_count(kg_stats.get('total_edges', 0))}")
        print(f"NetworkX: {Colors.OKGREEN if kg_stats.get('use_networkx') else Colors.WARNING}{'✅' if kg_stats.get('use_networkx') else '⚠️ Fallback'}{Colors.ENDC}")
        
        # Distribuzione nodi
        node_stats = kg_stats.get('node_stats', {})
        for node_type, count in node_stats.items():
            print(f"  - {node_type}: {format_count(count)}")
        
        # Top-5 Pattern Nuovi
        print_section("🏆 Top-5 Pattern Recenti")
        
        top_patterns = pattern_extractor.get_top_patterns(limit=5, sort_by="last_seen")
        
        if top_patterns:
            for i, pattern in enumerate(top_patterns, 1):
                print(f"{i}. {Colors.BOLD}{pattern.pattern_name}{Colors.ENDC}")
                print(f"   Support: {format_count(pattern.support)} | Confidence: {format_number(pattern.confidence)}")
                print(f"   Strategie: {', '.join(pattern.patch_strategies[:3])}")
                if len(pattern.patch_strategies) > 3:
                    print(f"   (+{len(pattern.patch_strategies)-3} altre)")
                print()
        else:
            print(f"{Colors.WARNING}⚠️ Nessun pattern trovato{Colors.ENDC}")
        
        # Performance Summary
        print_section("⚡ Performance Summary")
        
        cache_stats = learning_stats.get('buffer_sizes', {})
        print(f"Buffer patch: {cache_stats.get('patch_buffer', 0)}")
        print(f"Buffer pattern: {cache_stats.get('pattern_buffer', 0)}")
        print(f"Buffer metrics: {cache_stats.get('metrics_buffer', 0)}")
        
        print(f"\n{Colors.OKGREEN}✅ Status completato{Colors.ENDC}")
        return 0
        
    except Exception as e:
        print(f"{Colors.FAIL}❌ Errore: {e}{Colors.ENDC}")
        return 1


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="NEUROGLYPH CLI - Learning System Management",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Esempi:
  ng learner status                    # Status completo
  ng learner status --db custom.db     # Database custom
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Comandi disponibili')
    
    # Comando learner
    learner_parser = subparsers.add_parser('learner', help='Learning system management')
    learner_subparsers = learner_parser.add_subparsers(dest='learner_command')
    
    # Subcommando status
    status_parser = learner_subparsers.add_parser('status', help='Mostra status learning system')
    status_parser.add_argument('--db', default='learning.db', help='Percorso database (default: learning.db)')
    
    args = parser.parse_args()
    
    if args.command == 'learner':
        if args.learner_command == 'status':
            return learner_status(args.db)
        else:
            learner_parser.print_help()
            return 1
    else:
        parser.print_help()
        return 1


if __name__ == '__main__':
    sys.exit(main())
